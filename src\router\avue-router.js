import website from '@/config/website';
import { getToken } from '@/utils/auth';
import { buildHierarchyTree } from '@/utils/tree';
const IFrame = () => import('@/components/iframe/main.vue');
const modulesRoutes = import.meta.glob('/src/views/**/*.{vue,jsx}');
function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s);
}

/**
 * 将多级嵌套路由处理成一维数组
 * @param routesList 传入路由
 * @returns 返回处理后的一维路由
 */
function formatFlatteningRoutes(routesList) {
  if (routesList.length === 0) return routesList;
  let hierarchyList = buildHierarchyTree(routesList);
  for (let i = 0; i < hierarchyList.length; i++) {
    if (hierarchyList[i].children) {
      hierarchyList = hierarchyList
        .slice(0, i + 1)
        .concat(hierarchyList[i].children, hierarchyList.slice(i + 1));
    }
  }
  return hierarchyList;
}

/** 过滤后端传来的动态路由 重新生成规范路由 */
function addAsyncRoutes(arrRoutes) {
  if (!arrRoutes || !arrRoutes.length) return;
  const modulesRoutesKeys = Object.keys(modulesRoutes);
  arrRoutes.forEach(v => {
    // 将backstage属性加入meta，标识此路由为后端返回路由
    v.meta.backstage = true;
    // 父级的redirect属性取值：如果子级存在且父级的redirect属性不存在，默认取第一个子级的path；如果子级存在且父级的redirect属性存在，取存在的redirect属性，会覆盖默认值
    if (v?.children && v.children.length && !v.redirect) v.redirect = v.children[0].path;
    // 父级的name属性取值：如果子级存在且父级的name属性不存在，默认取第一个子级的name；如果子级存在且父级的name属性存在，取存在的name属性，会覆盖默认值（注意：测试中发现父级的name不能和子级name重复，如果重复会造成重定向无效（跳转404），所以这里给父级的name起名的时候后面会自动加上`Parent`，避免重复）
    if (v?.children && v.children.length && !v.name) v.name = v.children[0].name + 'Parent';
    if (v.query?.url) {
      v.component = IFrame;
    } else {
      // 对后端传component组件路径和不传做兼容（如果后端传component组件路径，那么path可以随便写，如果不传，component组件路径会跟path保持一致）
      const index = v?.component
        ? modulesRoutesKeys.findIndex(ev => ev.includes(v.component))
        : modulesRoutesKeys.findIndex(ev => ev.includes(v.path));
      v.component = modulesRoutes[modulesRoutesKeys[index]];
    }
    if (v?.children && v.children.length) {
      addAsyncRoutes(v.children);
    }
  });
  return arrRoutes;
}

let RouterPlugin = function () {
  this.$router = null;
  this.$store = null;
};
RouterPlugin.install = function (option = {}) {
  this.$router = option.router;
  this.$store = option.store;
  let i18n = option.i18n.global;
  this.$router.$avueRouter = {
    safe: this,
    // 设置标题
    setTitle: title => {
      const defaultTitle = i18n.t('title');
      title = title ? `${title} | ${defaultTitle}` : defaultTitle;
      document.title = title;
    },
    closeTag: value => {
      let tag = value || this.$store.getters.tag;
      if (typeof value === 'string') {
        tag = this.$store.getters.tagList.find(ele => ele.fullPath === value);
      }
      this.$store.commit('DEL_TAG', tag);
    },
    generateTitle: (item, props = {}) => {
      let query = item[props.query || 'query'] || {};
      let title = query.name || item[props.label || 'label'];
      let meta = item[props.meta || 'meta'] || {};
      let key = meta.i18n;
      if (key) {
        const hasKey = i18n.te('route.' + key);
        if (hasKey) return i18n.t('route.' + key);
      }
      return title ? title.split(',')[0] : title;
    },
    //动态路由
    formatRoutes: function (routeList = []) {
      if (routeList.length === 0) {
        return;
      } else {
        formatFlatteningRoutes(addAsyncRoutes(routeList)).map(v => {
          delete v.alias;
          // 防止重复添加路由
          if (
            this.safe.$router.options.routes[0].children.findIndex(
              value => value.path === v.path,
            ) !== -1
          ) {
            return;
          } else {
            // 切记将路由push到routes后还需要使用addRoute，这样路由才能正常跳转
            this.safe.$router.options.routes[0].children.push(v);
            // 最终路由进行升序
            if (!this.safe.$router.hasRoute(v?.name)) this.safe.$router.addRoute(v);
            if (v.layout === 'App') {
            } else { 
               const flattenRouters = this.safe.$router.getRoutes().find(n => n.path === '/wel');
              // 保持router.options.routes[0].children与path为"/wel"的children一致，防止数据不一致导致异常
              flattenRouters.children = this.safe.$router.options.routes[0].children;
              this.safe.$router.addRoute(flattenRouters);
            }
           
          }
        });
      }
    },
  };
};
export const formatPath = (ele, first) => {
  const propsDefault = website.menu;
  const icon = ele[propsDefault.icon];
  ele[propsDefault.icon] = !icon ? propsDefault.iconDefault : icon;
  ele.meta = {
    keepAlive: ele.isOpen === 2,
  };
  const iframeSrc = href => {
    // 替换&为#
    let processedHref = href.replace(/&/g, '#');

    // 检查URL中是否包含${token}，如果有则使用getToken()替换
    if (processedHref.includes('${token}')) {
      const token = getToken();
      processedHref = processedHref.replace(/\${token}/g, token);
    }
    return processedHref;
  };
  const isChild = !!(ele[propsDefault.children] && ele[propsDefault.children].length !== 0);
  if (!isChild && first) {
    ele.component = 'views' + ele[propsDefault.path];
    if (isURL(ele[propsDefault.href])) {
      let href = ele[propsDefault.href];
      ele[propsDefault.query] = {
        url: iframeSrc(href),
      };
    }
  } else {
    ele[propsDefault.children] &&
      ele[propsDefault.children].forEach(child => {
        child.component = 'views' + child[propsDefault.path];
        child.meta = {
          keepAlive: child.isOpen === 2,
        };
        if (isURL(child[propsDefault.href])) {
          let href = child[propsDefault.href];
          child[propsDefault.path] = ele[propsDefault.path] + '/' + child.code;
          child[propsDefault.query] = {
            url: iframeSrc(href),
          };
        }
        formatPath(child);
      });
  }
};
export default RouterPlugin;
