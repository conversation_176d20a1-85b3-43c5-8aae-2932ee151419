<template>
  <div class="calendar-parent-example">
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>日历组件父组件示例</span>
          <el-button type="primary" @click="showCalendar = true">
            选择日期范围
          </el-button>
        </div>
      </template>
      
      <!-- 接收到的数据展示 -->
      <div class="received-data">
        <h3>从子组件接收到的数据：</h3>
        <div class="data-display">
          <pre>{{ JSON.stringify(dateRange, null, 2) }}</pre>
        </div>
      </div>
      
      <!-- 格式化显示 -->
      <div class="formatted-display">
        <h3>格式化显示：</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">开始日期：</span>
            <span class="value">{{ dateRange.start || '未选择' }}</span>
          </div>
          <div class="info-item">
            <span class="label">结束日期：</span>
            <span class="value">{{ dateRange.end || '未选择' }}</span>
          </div>
          <div class="info-item">
            <span class="label">时长：</span>
            <span class="value duration">{{ dateRange.durationText || '0天' }}</span>
          </div>
          <div class="info-item">
            <span class="label">天数：</span>
            <span class="value">{{ dateRange.duration || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">是否跨年：</span>
            <span :class="['value', dateRange.isCrossYear ? 'cross-year' : 'same-year']">
              {{ dateRange.isCrossYear ? '是' : '否' }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 业务逻辑示例 -->
      <div class="business-logic">
        <h3>业务逻辑示例：</h3>
        <div class="logic-examples">
          <div class="logic-item">
            <span class="logic-label">短期任务（≤7天）：</span>
            <span :class="['logic-value', isShortTerm ? 'active' : 'inactive']">
              {{ isShortTerm ? '是' : '否' }}
            </span>
          </div>
          <div class="logic-item">
            <span class="logic-label">中期任务（8-30天）：</span>
            <span :class="['logic-value', isMediumTerm ? 'active' : 'inactive']">
              {{ isMediumTerm ? '是' : '否' }}
            </span>
          </div>
          <div class="logic-item">
            <span class="logic-label">长期任务（>30天）：</span>
            <span :class="['logic-value', isLongTerm ? 'active' : 'inactive']">
              {{ isLongTerm ? '是' : '否' }}
            </span>
          </div>
          <div class="logic-item">
            <span class="logic-label">跨年任务：</span>
            <span :class="['logic-value', dateRange.isCrossYear ? 'active' : 'inactive']">
              {{ dateRange.isCrossYear ? '是' : '否' }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="handleSave" type="primary" :disabled="!isValidSelection">
          保存选择
        </el-button>
        <el-button @click="handleReset" type="default">
          重置
        </el-button>
        <el-button @click="handleExport" type="success" :disabled="!isValidSelection">
          导出数据
        </el-button>
      </div>
    </el-card>
    
    <!-- 日历弹窗 -->
    <el-dialog 
      v-model="showCalendar" 
      title="选择日期范围" 
      width="80%"
      :before-close="handleCalendarClose"
    >
      <CalendarPanel 
        v-model="dateRange"
        @close="showCalendar = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CalendarPanel from '@/views/alarmSettings/components/CalendarPanel.vue'

// 响应式数据
const showCalendar = ref(false)
const dateRange = ref({
  start: '',
  end: '',
  duration: 0,
  durationText: '0天',
  isCrossYear: false
})

// 计算属性
const isValidSelection = computed(() => {
  return dateRange.value.start && dateRange.value.end && dateRange.value.duration > 0
})

const isShortTerm = computed(() => {
  return dateRange.value.duration > 0 && dateRange.value.duration <= 7
})

const isMediumTerm = computed(() => {
  return dateRange.value.duration > 7 && dateRange.value.duration <= 30
})

const isLongTerm = computed(() => {
  return dateRange.value.duration > 30
})

// 监听数据变化
watch(
  () => dateRange.value,
  (newValue, oldValue) => {
    console.log('日期范围数据变化:', {
      old: oldValue,
      new: newValue
    })
    
    // 可以在这里添加业务逻辑
    if (newValue.duration > 365) {
      ElMessage.warning('选择的时长超过一年，请确认是否正确')
    }
  },
  { deep: true }
)

// 方法
const handleSave = async () => {
  try {
    await ElMessageBox.confirm(
      `确认保存以下日期范围吗？\n开始：${dateRange.value.start}\n结束：${dateRange.value.end}\n时长：${dateRange.value.durationText}`,
      '确认保存',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 模拟保存操作
    console.log('保存数据:', dateRange.value)
    ElMessage.success('保存成功！')
    
  } catch (error) {
    ElMessage.info('已取消保存')
  }
}

const handleReset = () => {
  dateRange.value = {
    start: '',
    end: '',
    duration: 0,
    durationText: '0天',
    isCrossYear: false
  }
  ElMessage.info('已重置选择')
}

const handleExport = () => {
  const exportData = {
    ...dateRange.value,
    exportTime: new Date().toISOString(),
    taskType: isShortTerm.value ? '短期' : isMediumTerm.value ? '中期' : '长期'
  }
  
  console.log('导出数据:', exportData)
  
  // 模拟导出
  const dataStr = JSON.stringify(exportData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `date-range-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('导出成功！')
}

const handleCalendarClose = (done) => {
  if (isValidSelection.value) {
    ElMessageBox.confirm(
      '当前已有选择，确认关闭吗？',
      '确认关闭',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      done()
    }).catch(() => {
      // 取消关闭
    })
  } else {
    done()
  }
}
</script>

<style scoped>
.calendar-parent-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.example-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.received-data {
  margin-bottom: 30px;
}

.data-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.data-display pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #333;
}

.formatted-display {
  margin-bottom: 30px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 6px;
  border: 1px solid #cbd5e1;
}

.label {
  font-weight: 500;
  color: #475569;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #1e293b;
  font-weight: 600;
}

.value.duration {
  color: #0ea5e9;
  background: rgba(14, 165, 233, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.value.cross-year {
  color: #f59e0b;
}

.value.same-year {
  color: #10b981;
}

.business-logic {
  margin-bottom: 30px;
}

.logic-examples {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.logic-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.logic-label {
  font-weight: 500;
  color: #475569;
  margin-right: 10px;
  min-width: 120px;
}

.logic-value.active {
  color: #10b981;
  font-weight: 600;
}

.logic-value.inactive {
  color: #6b7280;
}

.actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

h3 {
  color: #1e293b;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}
</style>
