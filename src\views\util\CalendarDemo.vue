<template>
  <div class="calendar-demo">
    <div class="demo-header">
      <h1>现代化日历组件演示</h1>
      <p>重新设计的日历组件，专为PC端桌面浏览器优化</p>
      <div class="feature-list">
        <span class="feature-tag">✅ 移除星期显示</span>
        <span class="feature-tag">✅ 现代化设计</span>
        <span class="feature-tag">✅ PC端优化</span>
        <span class="feature-tag">✅ 兼容性修复</span>
      </div>
    </div>

    <div class="demo-content">
      <CalendarPanel v-model="selectedRange" />

      <div class="result-display">
        <h3>选择结果：</h3>
        <div class="result-item">
          <span class="label">起始日期：</span>
          <span class="value">{{ selectedRange.start || '未选择' }}</span>
        </div>
        <div class="result-item">
          <span class="label">结束日期：</span>
          <span class="value">{{ selectedRange.end || '未选择' }}</span>
        </div>
        <div class="result-item" v-if="selectedRange.start && selectedRange.end">
          <span class="label">日期范围：</span>
          <span class="value range">{{ selectedRange.start }} 至 {{ selectedRange.end }}</span>
        </div>
      </div>

      <div class="usage-tips">
        <h3>使用说明：</h3>
        <ul>
          <li>左侧面板选择起始日期，右侧面板选择结束日期</li>
          <li>支持跨年日期范围选择</li>
          <li>日期网格已正确对齐到星期，无需显示星期标题</li>
          <li>悬停和点击效果已优化，提供良好的交互反馈</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import CalendarPanel from './components/CalendarPanel.vue';

// 选择的日期范围
const selectedRange = ref({
  start: '',
  end: '',
});
</script>

<style scoped>
.calendar-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.demo-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demo-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.result-display {
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.result-display h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #64748b;
  min-width: 80px;
}

.value {
  color: #1e293b;
  font-weight: 600;
  padding: 4px 12px;
  background: #f1f5f9;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
