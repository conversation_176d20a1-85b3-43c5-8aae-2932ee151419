<!doctype html>
<html lang="en" data-theme="go">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <link rel="icon" href="/favicon.png" />
    <link rel="stylesheet" href="/iconfont/index.css" />
    <link rel="stylesheet" href="/iconfont/avue/iconfont.css" />
    <link rel="stylesheet" href="/iconfont/saber/iconfont.css" />
    <link rel="stylesheet" href="/iconfont/common/iconfont.css" />
    <link rel="stylesheet" href="/css/loading.css" />
    <link rel="stylesheet" href="/css/saber.css" />
    <title>学情预警系统</title>
  </head>

  <body class="theme-go">
    <div id="app">
      <div id="loader-wrapper">
        <div class="loader-content">
          <!-- 中心图标区域 -->
          <div class="loader-icon">
            <!-- 书本图标 -->
            <div class="book-icon">
              <div class="book-cover"></div>
              <div class="book-pages"></div>
            </div>

            <!-- 数据图表元素 -->
            <div class="chart-elements">
              <div class="chart-bar"></div>
              <div class="chart-bar"></div>
              <div class="chart-bar"></div>
              <div class="chart-bar"></div>
            </div>

            <!-- 预警雷达扫描 -->
            <div class="radar-scanner"></div>
          </div>

          <!-- 标题区域 -->
          <div class="loader-title">学情预警系统</div>
          <div class="loader-subtitle">智能分析 · 精准预警 · 个性化指导</div>

          <!-- 进度条 -->
          <div class="progress-container">
            <div class="progress-bar"></div>
          </div>

          <!-- 加载状态 -->
          <div class="loading-status" id="loading-status">正在初始化系统...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
