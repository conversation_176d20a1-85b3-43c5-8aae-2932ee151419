import { ref, toRefs } from 'vue';
import store from '@/store';
import { getYxListAPI, getZyListAPI, getBjListAPI } from '@/api/teachersStudentTable/student';
import { getList } from '@/api/system/dept';

const apis = {
  yx: { api: getYxListAPI, name: '院系', key: 'yxList' },
  zy: { api: getZyListAPI, name: '专业', key: 'zyList' },
  bj: { api: getBjListAPI, name: '班级', key: 'bjList' },
  dept: { api: getList, name: '部门', key: 'deptList', format: d => ({ label: d.deptName, value: d.id }) },
};

/**
 * 获取业务数据
 */
const useBusinessData = (...args) => {
  const res = ref({});
  return (() => {
    args.forEach(async dictType => {
      const apiInfo = apis[dictType];
      if (!apiInfo) {
        throw new Error(`“${dictType}”API接口不存在`);
      } else {
        res.value[apiInfo.key] = [];
        const dicts = store.getters.getDict(apiInfo.key);
        if (dicts) {
          res.value[apiInfo.key] = dicts;
        } else {
          apiInfo.api({}).then(resp => {
            const { code, data } = resp.data;
            if (code === 200) {
              res.value[apiInfo.key] = apiInfo.format
                ? data.map(apiInfo.format)
                : data.map(d => ({
                    label: d.name,
                    value: d.code,
                  }));
              store.dispatch('setDict', { key: apiInfo.key, value: res.value[apiInfo.key] });
            }
          });
        }
      }
    });
    return toRefs(res.value);
  })();
};
export default useBusinessData;
