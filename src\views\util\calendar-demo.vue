<template>
  <CalendarPanel v-model="selectedRange" />
</template>

<script setup>
import { ref, watch } from 'vue';
import CalendarPanel from './components/CalendarPanel.vue';

// 选中的日期范围
const selectedRange = ref({
  start: '',
  end: '',
});

// 监听日期范围变化
watch(
  selectedRange,
  newRange => {
    console.log('选中的日期范围:', newRange);
  },
  { deep: true },
);
</script>

<style scoped>
.calendar-demo-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.calendar-panel-demo {
  max-width: 860px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.calendar-panel-demo h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 24px;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.demo-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.demo-section p {
  margin: 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}
</style>
