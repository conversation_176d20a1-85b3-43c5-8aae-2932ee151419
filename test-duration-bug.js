// 测试时长计算bug的脚本

// 模拟当前的计算逻辑
const currentYear = new Date().getFullYear()

const parseDate = dateStr => {
  if (!dateStr) return null
  const [month, day] = dateStr.split('-').map(Number)
  return { month, day, year: currentYear }
}

const inferDateYear = (startDate, endDate) => {
  if (!startDate || !endDate) return { startDate, endDate }

  // 创建同年的日期对象进行比较
  const startDateSameYear = new Date(currentYear, startDate.month - 1, startDate.day)
  const endDateSameYear = new Date(currentYear, endDate.month - 1, endDate.day)

  // 如果结束日期早于开始日期，说明是跨年范围
  const isCrossYear = endDateSameYear < startDateSameYear

  if (isCrossYear) {
    return {
      startDate: { ...startDate, year: currentYear },
      endDate: { ...endDate, year: currentYear + 1 },
    }
  }

  // 同年情况
  return {
    startDate: { ...startDate, year: currentYear },
    endDate: { ...endDate, year: currentYear },
  }
}

const calculateDurationInfo = (startStr, endStr) => {
  const start = parseDate(startStr)
  const end = parseDate(endStr)

  if (!start || !end) {
    return { isValid: false, days: 0, text: '0天', isCrossYear: false, warning: null }
  }

  // 推断年份
  const { startDate: inferredStart, endDate: inferredEnd } = inferDateYear(start, end)

  // 创建完整的日期对象
  const startDateObj = new Date(inferredStart.year, inferredStart.month - 1, inferredStart.day)
  const endDateObj = new Date(inferredEnd.year, inferredEnd.month - 1, inferredEnd.day)

  console.log('开始日期对象:', startDateObj)
  console.log('结束日期对象:', endDateObj)

  // 计算时间差（毫秒）
  const timeDiff = endDateObj.getTime() - startDateObj.getTime()
  console.log('时间差(毫秒):', timeDiff)
  console.log('时间差(天):', timeDiff / (1000 * 60 * 60 * 24))

  // 转换为天数（包含开始和结束日期）
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24)) + 1

  // 判断是否跨年
  const isCrossYear = inferredEnd.year > inferredStart.year

  return {
    isValid: true,
    days: Math.max(daysDiff, 0),
    text: daysDiff > 0 ? `${daysDiff}天` : '0天',
    isCrossYear,
    warning: null
  }
}

// 测试用例
console.log('=== 测试用例 ===')

console.log('\n1. 2月1号到2月29号（应该是29天）:')
const result1 = calculateDurationInfo('02-01', '02-29')
console.log('结果:', result1)

console.log('\n2. 2月1号到3月1号（应该是30天）:')
const result2 = calculateDurationInfo('02-01', '03-01')
console.log('结果:', result2)

console.log('\n3. 2月28号到3月1号（应该是2天或3天）:')
const result3 = calculateDurationInfo('02-28', '03-01')
console.log('结果:', result3)

console.log('\n4. 3月15号到3月20号（应该是6天）:')
const result4 = calculateDurationInfo('03-15', '03-20')
console.log('结果:', result4)

console.log('\n5. 12月30号到1月2号（应该是4天）:')
const result5 = calculateDurationInfo('12-30', '01-02')
console.log('结果:', result5)
