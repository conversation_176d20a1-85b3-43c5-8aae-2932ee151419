/**
 * v-perms 权限控制自定义指令
 * 简化版实现，仅支持基于权限码的验证
 * 
 * 使用方式：
 * v-perms="'user:add'"                    // 单个权限码
 * v-perms="['user:edit', 'user:update']"  // 权限码数组（OR逻辑）
 */

import store from '@/store'

/**
 * 检查用户是否拥有指定权限
 * @param {string|Array} permission - 权限码或权限码数组
 * @returns {boolean} - 是否有权限
 */
function hasPermission(permission) {
  // 获取用户权限数据
  const userPermissions = store.getters.permission || {}
  
  // 参数验证
  if (!permission) {
    console.warn('[v-perms] 权限参数不能为空')
    return false
  }
  
  try {
    // 单个权限码字符串
    if (typeof permission === 'string') {
      return !!userPermissions[permission]
    }
    
    // 权限码数组（OR逻辑：只要有一个权限码匹配就返回true）
    if (Array.isArray(permission)) {
      if (permission.length === 0) {
        console.warn('[v-perms] 权限码数组不能为空')
        return false
      }
      return permission.some(code => !!userPermissions[code])
    }
    
    // 不支持的参数类型
    console.warn('[v-perms] 不支持的权限参数类型:', typeof permission)
    return false
    
  } catch (error) {
    console.error('[v-perms] 权限验证出错:', error)
    return false
  }
}

/**
 * 处理元素的显示/隐藏
 * @param {HTMLElement} el - DOM元素
 * @param {boolean} hasAuth - 是否有权限
 */
function handleElementVisibility(el, hasAuth) {
  if (hasAuth) {
    // 有权限时，确保元素可见（如果之前被隐藏）
    if (el.style.display === 'none') {
      el.style.display = ''
    }
  } else {
    // 无权限时，从DOM中移除元素
    if (el.parentNode) {
      el.parentNode.removeChild(el)
    }
  }
}

/**
 * v-perms 指令定义
 */
export const perms = {
  // Vue 3 生命周期：元素挂载到DOM时
  mounted(el, binding) {
    const permission = binding.value
    const hasAuth = hasPermission(permission)
    handleElementVisibility(el, hasAuth)
  },
  
  // Vue 3 生命周期：指令绑定值更新时
  updated(el, binding) {
    const permission = binding.value
    const hasAuth = hasPermission(permission)
    handleElementVisibility(el, hasAuth)
  }
}
