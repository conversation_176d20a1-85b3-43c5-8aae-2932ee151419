# CalendarPanel 时长计算功能

## 概述

`CalendarPanel` 组件现已支持自动计算并传递时长信息给父组件。当用户选择日期范围时，组件会自动计算时长并通过 `v-model` 传递给父组件。

## 功能特性

- ✅ **自动时长计算**：选择日期后自动计算天数
- ✅ **跨年支持**：正确处理跨年日期范围
- ✅ **实时更新**：日期变化时时长信息自动更新
- ✅ **完整数据传递**：通过 modelValue 传递完整的时长信息
- ✅ **边界情况处理**：处理无效日期和异常情况

## 数据结构

### 输入/输出格式

```javascript
// modelValue 数据结构
{
  start: "03-15",           // 开始日期 (MM-DD格式)
  end: "03-20",             // 结束日期 (MM-DD格式)
  duration: 6,              // 时长天数（数字）
  durationText: "6天",      // 时长文本显示
  isCrossYear: false        // 是否跨年
}
```

### 字段说明

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `start` | String | 开始日期，MM-DD格式 | `"03-15"` |
| `end` | String | 结束日期，MM-DD格式 | `"03-20"` |
| `duration` | Number | 时长天数（包含开始和结束日期） | `6` |
| `durationText` | String | 格式化的时长文本 | `"6天"` |
| `isCrossYear` | Boolean | 是否为跨年日期范围 | `false` |

## 使用方法

### 1. 基础用法

```vue
<template>
  <div>
    <!-- 使用 v-model 双向绑定 -->
    <CalendarPanel v-model="dateRange" @close="handleClose" />
    
    <!-- 显示接收到的数据 -->
    <div v-if="dateRange.start && dateRange.end">
      <p>选择范围：{{ dateRange.start }} 到 {{ dateRange.end }}</p>
      <p>时长：{{ dateRange.durationText }}</p>
      <p v-if="dateRange.isCrossYear">跨年选择</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CalendarPanel from '@/views/alarmSettings/components/CalendarPanel.vue'

const dateRange = ref({
  start: '',
  end: '',
  duration: 0,
  durationText: '0天',
  isCrossYear: false
})

const handleClose = () => {
  console.log('日历关闭')
}
</script>
```

### 2. 监听数据变化

```vue
<script setup>
import { ref, watch } from 'vue'

const dateRange = ref({
  start: '',
  end: '',
  duration: 0,
  durationText: '0天',
  isCrossYear: false
})

// 监听数据变化
watch(
  () => dateRange.value,
  (newValue) => {
    console.log('日期范围变化:', newValue)
    
    // 根据时长执行不同的业务逻辑
    if (newValue.duration > 0) {
      if (newValue.duration <= 7) {
        console.log('短期任务')
      } else if (newValue.duration <= 30) {
        console.log('中期任务')
      } else {
        console.log('长期任务')
      }
      
      if (newValue.isCrossYear) {
        console.log('跨年任务，需要特殊处理')
      }
    }
  },
  { deep: true }
)
</script>
```

### 3. 业务逻辑示例

```vue
<script setup>
import { ref, computed } from 'vue'

const dateRange = ref({
  start: '',
  end: '',
  duration: 0,
  durationText: '0天',
  isCrossYear: false
})

// 基于时长的计算属性
const taskType = computed(() => {
  const { duration } = dateRange.value
  if (duration === 0) return '未选择'
  if (duration <= 7) return '短期任务'
  if (duration <= 30) return '中期任务'
  return '长期任务'
})

const urgencyLevel = computed(() => {
  const { duration, isCrossYear } = dateRange.value
  if (duration === 0) return 'none'
  if (isCrossYear) return 'high'
  if (duration <= 3) return 'urgent'
  if (duration <= 7) return 'normal'
  return 'low'
})

// 业务方法
const handleSave = () => {
  const { start, end, duration, durationText, isCrossYear } = dateRange.value
  
  if (!start || !end) {
    alert('请选择完整的日期范围')
    return
  }
  
  const taskData = {
    dateRange: { start, end },
    duration,
    durationText,
    isCrossYear,
    taskType: taskType.value,
    urgencyLevel: urgencyLevel.value,
    createdAt: new Date().toISOString()
  }
  
  console.log('保存任务数据:', taskData)
  // 调用API保存数据
}
</script>
```

## 时长计算逻辑

### 1. 计算规则

- **包含首尾**：时长计算包含开始日期和结束日期
- **跨年处理**：自动识别并正确计算跨年日期范围
- **边界情况**：处理无效日期和异常情况

### 2. 计算示例

```javascript
// 同年同月
start: "03-15", end: "03-20"
// 结果：duration = 6, durationText = "6天", isCrossYear = false

// 跨月
start: "03-28", end: "04-05" 
// 结果：duration = 9, durationText = "9天", isCrossYear = false

// 跨年
start: "12-28", end: "01-05"
// 结果：duration = 9, durationText = "9天", isCrossYear = true

// 单日
start: "06-15", end: "06-15"
// 结果：duration = 1, durationText = "1天", isCrossYear = false
```

### 3. 跨年判断逻辑

```javascript
// 跨年判断：如果结束日期在时间上早于开始日期，则为跨年
const startDateSameYear = new Date(currentYear, start.month - 1, start.day)
const endDateSameYear = new Date(currentYear, end.month - 1, end.day)
const isCrossYear = endDateSameYear < startDateSameYear

// 年份推断
if (isCrossYear) {
  startYear = currentYear
  endYear = currentYear + 1
} else {
  startYear = currentYear
  endYear = currentYear
}
```

## 错误处理

### 1. 数据验证

```javascript
// 组件内部会处理以下情况：
- 空日期：返回默认值 { duration: 0, durationText: '0天', isCrossYear: false }
- 无效日期：返回默认值并在控制台输出警告
- 日期顺序错误：显示警告信息但仍计算结果
```

### 2. 边界情况

```javascript
// 处理的边界情况：
- 开始日期晚于结束日期：显示警告但允许选择
- 时长超过365天：显示警告提示
- 日期格式错误：使用默认值
```

## 测试示例

### 1. 基础测试

```javascript
// 测试用例
const testCases = [
  {
    name: '同月测试',
    input: { start: '03-15', end: '03-17' },
    expected: { duration: 3, durationText: '3天', isCrossYear: false }
  },
  {
    name: '跨月测试', 
    input: { start: '03-25', end: '04-08' },
    expected: { duration: 15, durationText: '15天', isCrossYear: false }
  },
  {
    name: '跨年测试',
    input: { start: '12-28', end: '01-06' },
    expected: { duration: 10, durationText: '10天', isCrossYear: true }
  }
]
```

### 2. 集成测试

参考示例文件：
- `src/views/examples/CalendarDurationTest.vue` - 功能测试页面
- `src/views/examples/CalendarParentExample.vue` - 父组件集成示例

## 最佳实践

### 1. 数据初始化

```javascript
// 推荐：初始化时提供完整的数据结构
const dateRange = ref({
  start: '',
  end: '',
  duration: 0,
  durationText: '0天',
  isCrossYear: false
})
```

### 2. 数据监听

```javascript
// 推荐：使用深度监听来捕获所有变化
watch(
  () => dateRange.value,
  (newValue) => {
    // 处理数据变化
  },
  { deep: true }
)
```

### 3. 业务逻辑

```javascript
// 推荐：基于 duration 字段进行业务判断
const isValidRange = computed(() => dateRange.value.duration > 0)
const isShortTerm = computed(() => dateRange.value.duration <= 7)
const needsApproval = computed(() => dateRange.value.duration > 30)
```

## 更新日志

### v1.1.0
- 新增时长自动计算功能
- 新增 duration、durationText、isCrossYear 字段
- 优化跨年日期处理逻辑
- 完善边界情况处理
- 添加完整的测试示例

### v1.0.0
- 基础日期选择功能
- 双面板日历显示
- 基本的日期范围选择
