import { watch, ref, computed, isRef, getCurrentScope, onScopeDispose } from 'vue';

const isBrowser = !!(
  typeof window !== 'undefined' &&
  window.document &&
  window.document.createElement
);

export function getTargetElement(target, defaultElement) {
  if (!isBrowser) {
    return undefined;
  }

  if (!target) {
    return defaultElement;
  }

  let targetElement;
  if (typeof target === 'function') {
    targetElement = target();
  } else if (isRef(target)) {
    targetElement = target.value?.$el ?? target.value;
  } else {
    targetElement = target;
  }
  return targetElement;
}

export function useResizeObserver(target, callback, options) {
  const { box = 'content-box', ...argsOptions } = options ?? {};
  const isSupported = ref(window && 'ResizeObserver' in window);
  let ob = null;
  const modelTargets = computed(() =>
    Array.isArray(target) ? target.map(curr => getTargetElement(curr)) : [getTargetElement(target)],
  );

  const dispose = () => {
    if (ob) {
      ob.disconnect();
      ob = null;
    }
  };

  const watcher = watch(
    modelTargets,
    elements => {
      dispose();

      if (isSupported.value && window) {
        ob = new ResizeObserver(callback);

        elements.forEach(curr => {
          curr &&
            ob?.observe(curr, {
              box,
              ...argsOptions,
            });
        });
      }
    },
    {
      flush: 'post',
      immediate: true,
    },
  );

  const stop = () => {
    dispose();
    watcher();
  };

  if (getCurrentScope()) {
    onScopeDispose(stop);
  }

  return {
    isSupported,
    stop,
  };
}
