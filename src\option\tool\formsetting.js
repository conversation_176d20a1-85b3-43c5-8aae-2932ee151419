export default {
  height: 'auto',
  calcHeight: 50,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  grid: true,
  selection: true,
  labelWidth: 120,
  searchLabelWidth: 100,
  menuWidth: 320,
  dialogWidth: 500,
  dialogClickModal: false,
  column: [
    {
      label: '数据源',
      labelTip: '数据源管理配置的数据源列表',
      prop: 'datasourceId',
      hide: true,
      editDisplay: false,
      viewDisplay: false,
      span: 24,
      type: 'select',
      dicUrl: '/blade-develop/datasource/select',
      props: {
        label: 'name',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '请选择数据源',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '物理表名',
      labelTip: '从数据源列表选择的物理表名',
      filterable: true,
      prop: 'modelTable',
      type: 'tree',
      span: 24,
      hide: true,
      editDisplay: false,
      viewDisplay: false,
      dicData: [],
      props: {
        label: 'comment',
        value: 'name',
      },
      rules: [
        {
          required: true,
          message: '请输入数据库表名',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '表单名称',
      labelTip: '用于定义本配置的代表名称，默认为数据库表说明',
      prop: 'name',
      gridRow: true,
      search: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入表单名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '关联表名',
      labelTip: '用于定义本配置的代表编号，默认为数据库表名，在代码快速生成时会关联匹配',
      prop: 'code',
      gridRow: true,
      search: true,
      disabled: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入表单编号',
          trigger: 'blur',
        },
      ],
    },
  ],
};
