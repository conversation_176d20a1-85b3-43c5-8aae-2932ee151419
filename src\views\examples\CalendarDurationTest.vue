<template>
  <div class="calendar-duration-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>日历时长计算功能测试</span>
          <el-button type="primary" @click="showCalendar = true">
            打开日历选择器
          </el-button>
        </div>
      </template>
      
      <!-- 当前选择显示 -->
      <div class="current-selection">
        <h3>当前选择：</h3>
        <div class="selection-info">
          <div class="date-item">
            <span class="label">开始日期：</span>
            <span class="value">{{ selectedDates.start || '未选择' }}</span>
          </div>
          <div class="date-item">
            <span class="label">结束日期：</span>
            <span class="value">{{ selectedDates.end || '未选择' }}</span>
          </div>
          <div v-if="selectedDates.start && selectedDates.end" class="date-item">
            <span class="label">计算时长：</span>
            <span class="value duration">{{ calculatedDuration }}</span>
          </div>
        </div>
      </div>
      
      <!-- 测试用例 -->
      <div class="test-cases">
        <h3>预设测试用例：</h3>
        <div class="test-buttons">
          <el-button 
            v-for="testCase in testCases" 
            :key="testCase.name"
            @click="applyTestCase(testCase)"
            :type="testCase.type"
          >
            {{ testCase.name }}
          </el-button>
        </div>
      </div>
      
      <!-- 手动输入测试 -->
      <div class="manual-test">
        <h3>手动输入测试：</h3>
        <div class="input-group">
          <el-input 
            v-model="manualStart" 
            placeholder="开始日期 (MM-DD)"
            style="width: 150px; margin-right: 10px;"
          />
          <el-input 
            v-model="manualEnd" 
            placeholder="结束日期 (MM-DD)"
            style="width: 150px; margin-right: 10px;"
          />
          <el-button @click="applyManualTest" type="success">
            应用测试
          </el-button>
          <el-button @click="clearSelection" type="info">
            清空选择
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 日历弹窗 -->
    <el-dialog 
      v-model="showCalendar" 
      title="选择日期范围" 
      width="80%"
      :before-close="handleCalendarClose"
    >
      <CalendarPanel 
        v-model="selectedDates"
        @close="showCalendar = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import CalendarPanel from '@/views/alarmSettings/components/CalendarPanel.vue'

// 响应式数据
const showCalendar = ref(false)
const selectedDates = ref({
  start: '',
  end: ''
})
const manualStart = ref('')
const manualEnd = ref('')

// 测试用例
const testCases = ref([
  {
    name: '同月测试 (3天)',
    start: '03-15',
    end: '03-17',
    type: 'primary'
  },
  {
    name: '跨月测试 (15天)',
    start: '03-25',
    end: '04-08',
    type: 'success'
  },
  {
    name: '跨年测试 (10天)',
    start: '12-28',
    end: '01-06',
    type: 'warning'
  },
  {
    name: '长期测试 (90天)',
    start: '01-01',
    end: '03-31',
    type: 'info'
  },
  {
    name: '边界测试 (1天)',
    start: '06-15',
    end: '06-15',
    type: 'default'
  }
])

// 计算时长（用于显示）
const calculatedDuration = computed(() => {
  if (!selectedDates.value.start || !selectedDates.value.end) {
    return '未选择完整日期'
  }
  
  // 解析日期
  const parseDate = dateStr => {
    if (!dateStr) return null
    const [month, day] = dateStr.split('-').map(Number)
    return { month, day }
  }
  
  const start = parseDate(selectedDates.value.start)
  const end = parseDate(selectedDates.value.end)
  
  if (!start || !end) return '日期格式错误'
  
  // 获取当前年份
  const currentYear = new Date().getFullYear()
  
  // 推断年份（跨年逻辑）
  const startDateSameYear = new Date(currentYear, start.month - 1, start.day)
  const endDateSameYear = new Date(currentYear, end.month - 1, end.day)
  const isCrossYear = endDateSameYear < startDateSameYear
  
  let startDateObj, endDateObj
  if (isCrossYear) {
    startDateObj = new Date(currentYear, start.month - 1, start.day)
    endDateObj = new Date(currentYear + 1, end.month - 1, end.day)
  } else {
    startDateObj = new Date(currentYear, start.month - 1, start.day)
    endDateObj = new Date(currentYear, end.month - 1, end.day)
  }
  
  // 计算天数差
  const timeDiff = endDateObj.getTime() - startDateObj.getTime()
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24)) + 1
  
  let result = `${Math.max(daysDiff, 0)}天`
  if (isCrossYear) {
    result += ' (跨年)'
  }
  if (daysDiff <= 0) {
    result += ' ⚠️ 日期顺序错误'
  }
  
  return result
})

// 应用测试用例
const applyTestCase = (testCase) => {
  selectedDates.value = {
    start: testCase.start,
    end: testCase.end
  }
  ElMessage.success(`已应用测试用例：${testCase.name}`)
}

// 应用手动测试
const applyManualTest = () => {
  if (!manualStart.value || !manualEnd.value) {
    ElMessage.warning('请输入完整的开始和结束日期')
    return
  }
  
  // 验证日期格式
  const dateRegex = /^\d{2}-\d{2}$/
  if (!dateRegex.test(manualStart.value) || !dateRegex.test(manualEnd.value)) {
    ElMessage.error('日期格式错误，请使用 MM-DD 格式')
    return
  }
  
  selectedDates.value = {
    start: manualStart.value,
    end: manualEnd.value
  }
  ElMessage.success('已应用手动输入的日期')
}

// 清空选择
const clearSelection = () => {
  selectedDates.value = {
    start: '',
    end: ''
  }
  manualStart.value = ''
  manualEnd.value = ''
  ElMessage.info('已清空所有选择')
}

// 处理日历关闭
const handleCalendarClose = (done) => {
  ElMessage.info('日历选择器已关闭')
  done()
}
</script>

<style scoped>
.calendar-duration-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-selection {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.date-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #475569;
  width: 80px;
}

.value {
  color: #1e293b;
  font-weight: 600;
}

.value.duration {
  color: #0ea5e9;
  background: rgba(14, 165, 233, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.test-cases {
  margin-bottom: 30px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.manual-test {
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

h3 {
  color: #1e293b;
  margin-bottom: 15px;
  font-size: 16px;
}
</style>
